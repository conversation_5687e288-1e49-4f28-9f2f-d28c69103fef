/**
 * EnhancedDebugPanel.tsx
 * 
 * 增强的调试面板组件
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  Tabs,
  Table,
  Tree,
  Button,
  Input,
  Select,
  Switch,
  Space,
  Typography,
  Tag,
  Tooltip,
  Badge,
  Alert,
  Collapse,
  Progress,
  Divider,
  message
} from 'antd';
import {
  BugOutlined,
  PlayCircleOutlined,
  PauseOutlined,
  StepForwardOutlined,
  ClearOutlined,
  DownloadOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;
const { Search } = Input;

/**
 * 调试日志接口
 */
interface DebugLog {
  id: string;
  timestamp: number;
  level: 'debug' | 'info' | 'warn' | 'error';
  category: string;
  message: string;
  data?: any;
  nodeId?: string;
  nodeName?: string;
  stackTrace?: string;
}

/**
 * 断点接口
 */
interface Breakpoint {
  id: string;
  nodeId: string;
  nodeName: string;
  condition?: string;
  enabled: boolean;
  hitCount: number;
  lastHit?: number;
}

/**
 * 变量监视接口
 */
interface VariableWatch {
  id: string;
  name: string;
  expression: string;
  value: any;
  type: string;
  lastUpdate: number;
}

/**
 * 执行状态接口
 */
interface ExecutionState {
  isRunning: boolean;
  isPaused: boolean;
  currentNode?: string;
  executionStack: string[];
  variables: Record<string, any>;
  stepCount: number;
  startTime?: number;
  pauseTime?: number;
}

/**
 * 增强调试面板属性
 */
interface EnhancedDebugPanelProps {
  scriptId: string;
  isExecuting: boolean;
  onBreakpointToggle?: (nodeId: string, enabled: boolean) => void;
  onStepExecution?: () => void;
  onResumeExecution?: () => void;
  onStopExecution?: () => void;
}

/**
 * 增强调试面板组件
 */
const EnhancedDebugPanel: React.FC<EnhancedDebugPanelProps> = ({
  scriptId,
  isExecuting,
  onBreakpointToggle,
  onStepExecution,
  onResumeExecution,
  onStopExecution
}) => {
  const { t } = useTranslation();
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [breakpoints, setBreakpoints] = useState<Breakpoint[]>([]);
  const [watches, setWatches] = useState<VariableWatch[]>([]);
  const [executionState, setExecutionState] = useState<ExecutionState>({
    isRunning: false,
    isPaused: false,
    executionStack: [],
    variables: {},
    stepCount: 0
  });
  const [logFilter, setLogFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoScroll, setAutoScroll] = useState(true);
  const [maxLogs] = useState(1000);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // 添加调试日志
  const addLog = useCallback((log: Omit<DebugLog, 'id' | 'timestamp'>) => {
    const newLog: DebugLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      ...log
    };

    setLogs(prev => {
      const updated = [newLog, ...prev];
      return updated.slice(0, maxLogs);
    });

    if (autoScroll && logsEndRef.current) {
      setTimeout(() => {
        logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [maxLogs, autoScroll]);

  // 清除日志
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // 添加断点
  const addBreakpoint = useCallback((nodeId: string, nodeName: string, condition?: string) => {
    const newBreakpoint: Breakpoint = {
      id: `bp_${nodeId}`,
      nodeId,
      nodeName,
      condition,
      enabled: true,
      hitCount: 0
    };

    setBreakpoints(prev => {
      const existing = prev.find(bp => bp.nodeId === nodeId);
      if (existing) {
        return prev.map(bp => bp.nodeId === nodeId ? { ...bp, enabled: !bp.enabled } : bp);
      }
      return [...prev, newBreakpoint];
    });

    onBreakpointToggle?.(nodeId, true);
  }, [onBreakpointToggle]);

  // 移除断点
  const removeBreakpoint = useCallback((nodeId: string) => {
    setBreakpoints(prev => prev.filter(bp => bp.nodeId !== nodeId));
    onBreakpointToggle?.(nodeId, false);
  }, [onBreakpointToggle]);

  // 切换断点状态
  const toggleBreakpoint = useCallback((nodeId: string) => {
    setBreakpoints(prev => 
      prev.map(bp => 
        bp.nodeId === nodeId 
          ? { ...bp, enabled: !bp.enabled }
          : bp
      )
    );
  }, []);

  // 添加变量监视
  const addWatch = useCallback((name: string, expression: string) => {
    const newWatch: VariableWatch = {
      id: `watch_${Date.now()}`,
      name,
      expression,
      value: undefined,
      type: 'unknown',
      lastUpdate: Date.now()
    };

    setWatches(prev => [...prev, newWatch]);
  }, []);

  // 移除变量监视
  const removeWatch = useCallback((id: string) => {
    setWatches(prev => prev.filter(w => w.id !== id));
  }, []);

  // 更新执行状态
  // const updateExecutionState = useCallback((updates: Partial<ExecutionState>) => {
  //   setExecutionState(prev => ({ ...prev, ...updates }));
  // }, []);

  // 导出调试日志
  const exportLogs = useCallback(() => {
    const exportData = {
      scriptId,
      timestamp: new Date().toISOString(),
      logs: logs,
      breakpoints: breakpoints,
      watches: watches,
      executionState: executionState
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `debug-log-${scriptId}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [scriptId, logs, breakpoints, watches, executionState]);

  // 过滤日志
  const filteredLogs = logs.filter(log => {
    if (logFilter !== 'all' && log.level !== logFilter) return false;
    if (searchTerm && !log.message.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });

  // 日志表格列定义
  const logColumns = [
    {
      title: t('调试.时间'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (timestamp: number) => new Date(timestamp).toLocaleTimeString()
    },
    {
      title: t('调试.级别'),
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: string) => {
        const colors = {
          debug: 'default',
          info: 'blue',
          warn: 'orange',
          error: 'red'
        };
        return <Tag color={colors[level as keyof typeof colors]}>{level.toUpperCase()}</Tag>;
      }
    },
    {
      title: t('调试.分类'),
      dataIndex: 'category',
      key: 'category',
      width: 100
    },
    {
      title: t('调试.节点'),
      dataIndex: 'nodeName',
      key: 'nodeName',
      width: 120,
      render: (nodeName: string) => nodeName || '-'
    },
    {
      title: t('调试.消息'),
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    }
  ];

  // 断点表格列定义
  const breakpointColumns = [
    {
      title: t('调试.启用'),
      dataIndex: 'enabled',
      key: 'enabled',
      width: 60,
      render: (enabled: boolean, record: Breakpoint) => (
        <Switch
          size="small"
          checked={enabled}
          onChange={() => toggleBreakpoint(record.nodeId)}
        />
      )
    },
    {
      title: t('调试.节点'),
      dataIndex: 'nodeName',
      key: 'nodeName'
    },
    {
      title: t('调试.条件'),
      dataIndex: 'condition',
      key: 'condition',
      render: (condition: string) => condition || t('调试.无条件')
    },
    {
      title: t('调试.命中次数'),
      dataIndex: 'hitCount',
      key: 'hitCount',
      width: 100
    },
    {
      title: t('调试.操作'),
      key: 'actions',
      width: 80,
      render: (_: any, record: Breakpoint) => (
        <Button
          size="small"
          danger
          onClick={() => removeBreakpoint(record.nodeId)}
        >
          {t('调试.删除')}
        </Button>
      )
    }
  ];

  // 模拟添加一些调试日志
  useEffect(() => {
    if (isExecuting) {
      const interval = setInterval(() => {
        addLog({
          level: 'info',
          category: 'execution',
          message: `执行节点: Node_${Math.floor(Math.random() * 10)}`,
          nodeId: `node_${Math.floor(Math.random() * 10)}`,
          nodeName: `节点${Math.floor(Math.random() * 10)}`
        });
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [isExecuting, addLog]);

  return (
    <Card
      title={
        <Space>
          <BugOutlined />
          <span>{t('调试.调试面板')}</span>
          <Badge 
            status={isExecuting ? 'processing' : 'default'} 
            text={isExecuting ? t('调试.执行中') : t('调试.已停止')}
          />
        </Space>
      }
      extra={
        <Space>
          <Button
            size="small"
            icon={<ClearOutlined />}
            onClick={clearLogs}
          >
            {t('调试.清除日志')}
          </Button>
          <Button
            size="small"
            icon={<DownloadOutlined />}
            onClick={exportLogs}
          >
            {t('调试.导出')}
          </Button>
        </Space>
      }
      style={{ height: '100%' }}
      bodyStyle={{ padding: '16px', height: 'calc(100% - 60px)', overflow: 'hidden' }}
    >
      <Tabs defaultActiveKey="logs" style={{ height: '100%' }}>
        {/* 调试日志 */}
        <TabPane tab={t('调试.日志')} key="logs">
          <div style={{ marginBottom: '16px' }}>
            <Space>
              <Search
                placeholder={t('调试.搜索日志') as string}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: 200 }}
              />
              <Select
                value={logFilter}
                onChange={setLogFilter}
                style={{ width: 120 }}
              >
                <Option value="all">{t('调试.全部')}</Option>
                <Option value="debug">{t('调试.调试')}</Option>
                <Option value="info">{t('调试.信息')}</Option>
                <Option value="warn">{t('调试.警告')}</Option>
                <Option value="error">{t('调试.错误')}</Option>
              </Select>
              <Switch
                checked={autoScroll}
                onChange={setAutoScroll}
                checkedChildren={t('调试.自动滚动')}
                unCheckedChildren={t('调试.手动滚动')}
              />
            </Space>
          </div>
          
          <Table
            columns={logColumns}
            dataSource={filteredLogs}
            rowKey="id"
            size="small"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `${t('调试.共')} ${total} ${t('调试.条日志')}`
            }}
            scroll={{ y: 400 }}
          />
          <div ref={logsEndRef} />
        </TabPane>

        {/* 断点管理 */}
        <TabPane tab={t('调试.断点')} key="breakpoints">
          <div style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              onClick={() => {
                // 模拟添加断点
                addBreakpoint(`node_${Date.now()}`, `节点${Math.floor(Math.random() * 10)}`);
              }}
            >
              {t('调试.添加断点')}
            </Button>
          </div>
          
          <Table
            columns={breakpointColumns}
            dataSource={breakpoints}
            rowKey="id"
            size="small"
            pagination={false}
          />
        </TabPane>

        {/* 变量监视 */}
        <TabPane tab={t('调试.变量')} key="variables">
          <div style={{ marginBottom: '16px' }}>
            <Space>
              <Input
                placeholder={t('调试.变量名') as string}
                style={{ width: 120 }}
              />
              <Input
                placeholder={t('调试.表达式') as string}
                style={{ width: 150 }}
              />
              <Button
                type="primary"
                onClick={() => {
                  // 模拟添加变量监视
                  addWatch(`var_${Date.now()}`, 'someExpression');
                }}
              >
                {t('调试.添加监视')}
              </Button>
            </Space>
          </div>
          
          <Collapse>
            {watches.map(watch => (
              <Panel
                header={
                  <Space>
                    <Text strong>{watch.name}</Text>
                    <Tag>{watch.type}</Tag>
                  </Space>
                }
                key={watch.id}
                extra={
                  <Button
                    size="small"
                    danger
                    onClick={(e) => {
                      e.stopPropagation();
                      removeWatch(watch.id);
                    }}
                  >
                    {t('调试.删除')}
                  </Button>
                }
              >
                <Paragraph>
                  <Text strong>{t('调试.表达式')}: </Text>
                  <Text code>{watch.expression}</Text>
                </Paragraph>
                <Paragraph>
                  <Text strong>{t('调试.值')}: </Text>
                  <Text>{JSON.stringify(watch.value)}</Text>
                </Paragraph>
                <Paragraph>
                  <Text strong>{t('调试.最后更新')}: </Text>
                  <Text>{new Date(watch.lastUpdate).toLocaleString()}</Text>
                </Paragraph>
              </Panel>
            ))}
          </Collapse>
        </TabPane>

        {/* 执行状态 */}
        <TabPane tab={t('调试.执行状态')} key="execution">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small" title={t('调试.执行控制')}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={onResumeExecution}
                  disabled={!executionState.isPaused}
                >
                  {t('调试.继续')}
                </Button>
                <Button
                  icon={<PauseOutlined />}
                  disabled={!executionState.isRunning}
                >
                  {t('调试.暂停')}
                </Button>
                <Button
                  icon={<StepForwardOutlined />}
                  onClick={onStepExecution}
                  disabled={!executionState.isPaused}
                >
                  {t('调试.单步执行')}
                </Button>
                <Button
                  danger
                  icon={<CloseCircleOutlined />}
                  onClick={onStopExecution}
                  disabled={!executionState.isRunning}
                >
                  {t('调试.停止')}
                </Button>
              </Space>
            </Card>

            <Card size="small" title={t('调试.执行信息')}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>{t('调试.当前节点')}: </Text>
                  <Text>{executionState.currentNode || t('调试.无')}</Text>
                </div>
                <div>
                  <Text strong>{t('调试.执行步数')}: </Text>
                  <Text>{executionState.stepCount}</Text>
                </div>
                <div>
                  <Text strong>{t('调试.执行栈深度')}: </Text>
                  <Text>{executionState.executionStack.length}</Text>
                </div>
                {executionState.startTime && (
                  <div>
                    <Text strong>{t('调试.执行时间')}: </Text>
                    <Text>
                      {Math.round((Date.now() - executionState.startTime) / 1000)}s
                    </Text>
                  </div>
                )}
              </Space>
            </Card>
          </Space>
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default EnhancedDebugPanel;
